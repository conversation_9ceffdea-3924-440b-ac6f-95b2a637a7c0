package mux

import (
	"net/http"
	"testing"
)

func TestRouterGroup(t *testing.T) {
	engine := New()

	// Test basic group creation
	v1 := engine.Group("/v1")
	if v1 == nil {
		t.Fatal("Group should not be nil")
	}

	if v1.prefix != "/v1" {
		t.<PERSON><PERSON>("Expected prefix to be '/v1', got '%s'", v1.prefix)
	}

	if v1.engine != engine {
		t.Error("Group should reference the engine")
	}
}

func TestRouterGroupRoutes(t *testing.T) {
	engine := New()

	// Create a group and register routes
	v1 := engine.Group("/v1")
	v1.GET("/users", func(c *Context) {
		c.String("v1 users")
	})

	v1.POST("/users", func(c *Context) {
		c.String("v1 create user")
	})

	// Test that routes are registered with correct paths
	handler := engine.findHandler("GET", "/v1/users")
	if handler == nil {
		t.Fatal("GET /v1/users route should be registered")
	}

	handler = engine.findHandler("POST", "/v1/users")
	if handler == nil {
		t.Fatal("POST /v1/users route should be registered")
	}

	// Test that routes without group prefix are not found
	handler = engine.findHandler("GET", "/users")
	if handler != nil {
		t.Error("GET /users route should not be found")
	}
}

func TestNestedRouterGroups(t *testing.T) {
	engine := New()

	// Create nested groups
	api := engine.Group("/api")
	v1 := api.Group("/v1")
	users := v1.Group("/users")

	users.GET("/:id", func(c *Context) {
		c.String("user %s", c.Param("id"))
	})

	// Test that nested route is registered correctly
	handler := engine.findHandler("GET", "/api/v1/users/123")
	if handler == nil {
		t.Fatal("GET /api/v1/users/123 route should be registered")
	}

	// Test parameter extraction
	handler, params := engine.findHandlerWithParams("GET", "/api/v1/users/123")
	if handler == nil {
		t.Fatal("Handler should be found")
	}

	if params["id"] != "123" {
		t.Errorf("Expected id to be '123', got '%s'", params["id"])
	}
}

func TestRouterGroupMiddleware(t *testing.T) {
	engine := New()

	middlewareExecuted := false
	testMiddleware := func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			middlewareExecuted = true
			c.Set("middleware", "executed")
			next(c)
		}
	}

	// Create group with middleware
	v1 := engine.Group("/v1")
	v1.Use(testMiddleware)

	handlerExecuted := false
	v1.GET("/test", func(c *Context) {
		handlerExecuted = true
		value, exists := c.Get("middleware")
		if !exists || value != "executed" {
			t.Error("Middleware should set context value")
		}
	})

	// Create a mock request
	req, _ := http.NewRequest("GET", "/v1/test", nil)
	ctx := engine.createContext(nil, req)

	// Execute the handler
	engine.handleRequest(ctx)

	if !middlewareExecuted {
		t.Error("Group middleware should be executed")
	}

	if !handlerExecuted {
		t.Error("Handler should be executed")
	}
}

func TestMultipleGroups(t *testing.T) {
	engine := New()

	// Create multiple groups
	v1 := engine.Group("/v1")
	v2 := engine.Group("/v2")

	v1.GET("/test", func(c *Context) {
		c.String("v1 test")
	})

	v2.GET("/test", func(c *Context) {
		c.String("v2 test")
	})

	// Test that both routes are registered
	handler1 := engine.findHandler("GET", "/v1/test")
	if handler1 == nil {
		t.Fatal("GET /v1/test route should be registered")
	}

	handler2 := engine.findHandler("GET", "/v2/test")
	if handler2 == nil {
		t.Fatal("GET /v2/test route should be registered")
	}

	// Test that they are different handlers
	if &handler1 == &handler2 {
		t.Error("Handlers should be different")
	}
}

func TestRouterGroupAllHTTPMethods(t *testing.T) {
	engine := New()
	group := engine.Group("/api")

	// Test all HTTP methods
	group.GET("/get", func(c *Context) { c.String("GET") })
	group.POST("/post", func(c *Context) { c.String("POST") })
	group.PUT("/put", func(c *Context) { c.String("PUT") })
	group.DELETE("/delete", func(c *Context) { c.String("DELETE") })
	group.PATCH("/patch", func(c *Context) { c.String("PATCH") })
	group.HEAD("/head", func(c *Context) { c.String("HEAD") })
	group.OPTIONS("/options", func(c *Context) { c.String("OPTIONS") })

	// Test that all routes are registered
	methods := []string{"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
	paths := []string{"/get", "/post", "/put", "/delete", "/patch", "/head", "/options"}

	for i, method := range methods {
		fullPath := "/api" + paths[i]
		handler := engine.findHandler(method, fullPath)
		if handler == nil {
			t.Errorf("%s %s route should be registered", method, fullPath)
		}
	}
}

func TestRouterGroupChaining(t *testing.T) {
	engine := New()

	// Test middleware chaining
	middleware1Executed := false
	middleware2Executed := false

	middleware1 := func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			middleware1Executed = true
			c.Set("middleware1", "executed")
			next(c)
		}
	}

	middleware2 := func(next HandlerFunc) HandlerFunc {
		return func(c *Context) {
			middleware2Executed = true
			c.Set("middleware2", "executed")
			next(c)
		}
	}

	// Chain middleware using Use method
	group := engine.Group("/test")
	group.Use(middleware1).Use(middleware2)

	handlerExecuted := false
	group.GET("/chain", func(c *Context) {
		handlerExecuted = true

		// Check that both middleware were executed
		if val, exists := c.Get("middleware1"); !exists || val != "executed" {
			t.Error("Middleware1 should be executed")
		}

		if val, exists := c.Get("middleware2"); !exists || val != "executed" {
			t.Error("Middleware2 should be executed")
		}
	})

	// Create a mock request
	req, _ := http.NewRequest("GET", "/test/chain", nil)
	ctx := engine.createContext(nil, req)

	// Execute the handler
	engine.handleRequest(ctx)

	if !middleware1Executed {
		t.Error("Middleware1 should be executed")
	}

	if !middleware2Executed {
		t.Error("Middleware2 should be executed")
	}

	if !handlerExecuted {
		t.Error("Handler should be executed")
	}
}

func TestEmptyGroupPrefix(t *testing.T) {
	engine := New()

	// Test group with empty prefix
	group := engine.Group("")
	group.GET("/test", func(c *Context) {
		c.String("test")
	})

	// Should register route at root level
	handler := engine.findHandler("GET", "/test")
	if handler == nil {
		t.Fatal("GET /test route should be registered")
	}
}
