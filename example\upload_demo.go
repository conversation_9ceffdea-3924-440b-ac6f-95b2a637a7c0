//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"

	mux "gitlink.org.cn/nanakura/gnet-mux"
)

func main() {
	router := mux.Default()

	// Set a lower memory limit for multipart forms (default is 32 MiB)
	router.MaxMultipartMemory = 8 << 20 // 8 MiB

	// Create upload directory if it doesn't exist
	if err := os.MkdirAll("./files", 0o755); err != nil {
		log.Fatal("Failed to create upload directory:", err)
	}

	// Single file upload endpoint
	router.POST("/upload", func(c *mux.Context) {
		// Single file
		file, err := c.FormFile("file")
		if err != nil {
			c.Status(http.StatusBadRequest).String(fmt.Sprintf("get form err: %s", err.Error()))
			return
		}

		log.Println(file.Filename)

		// Upload the file to specific dst.
		dst := filepath.Join("./files", file.Filename)
		if err := c.SaveUploadedFile(file, dst); err != nil {
			c.Status(http.StatusInternalServerError).String(fmt.Sprintf("upload file err: %s", err.Error()))
			return
		}

		c.Status(http.StatusOK).String(fmt.Sprintf("'%s' uploaded!", file.Filename))
	})

	// Multiple files upload endpoint
	router.POST("/upload/multiple", func(c *mux.Context) {
		// Multipart form
		form, err := c.MultipartForm()
		if err != nil {
			c.Status(http.StatusBadRequest).String(fmt.Sprintf("get form err: %s", err.Error()))
			return
		}
		files := form.File["files"]

		for _, file := range files {
			log.Println(file.Filename)

			// Upload the file to specific dst.
			dst := filepath.Join("./files", file.Filename)
			if err := c.SaveUploadedFile(file, dst); err != nil {
				c.Status(http.StatusInternalServerError).String(fmt.Sprintf("upload file err: %s", err.Error()))
				return
			}
		}
		c.Status(http.StatusOK).String(fmt.Sprintf("%d files uploaded!", len(files)))
	})

	// File upload form page
	router.GET("/", func(c *mux.Context) {
		c.HTML(`
<!DOCTYPE html>
<html>
<head>
    <title>File Upload Demo</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 600px; margin: 0 auto; }
        .form-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input[type="file"] { width: 100%; padding: 8px; border: 1px solid #ddd; }
        button { background: #007cba; color: white; padding: 10px 20px; border: none; cursor: pointer; }
        button:hover { background: #005a87; }
        .section { margin-bottom: 40px; padding: 20px; border: 1px solid #eee; }
        h2 { color: #333; }
    </style>
</head>
<body>
    <div class="container">
        <h1>File Upload Demo</h1>

        <div class="section">
            <h2>Single File Upload</h2>
            <form action="/upload" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="file">Choose file:</label>
                    <input type="file" name="file" id="file" required>
                </div>
                <button type="submit">Upload File</button>
            </form>
        </div>

        <div class="section">
            <h2>Multiple Files Upload</h2>
            <form action="/upload/multiple" method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label for="files">Choose files:</label>
                    <input type="file" name="files" id="files" multiple required>
                </div>
                <button type="submit">Upload Files</button>
            </form>
        </div>

        <div class="section">
            <h2>Uploaded Files</h2>
            <p><a href="/files">View uploaded files</a></p>
        </div>
    </div>
</body>
</html>`)
	})

	// Serve uploaded files
	router.Static("/files", "./files")

	// List uploaded files endpoint
	router.GET("/api/files", func(c *mux.Context) {
		files, err := os.ReadDir("./files")
		if err != nil {
			c.JSON(http.StatusOK, mux.H{"error": "Failed to read files directory"})
			return
		}

		var fileList []mux.H
		for _, file := range files {
			if !file.IsDir() {
				info, _ := file.Info()
				fileList = append(fileList, mux.H{
					"name": file.Name(),
					"size": info.Size(),
				})
			}
		}

		c.JSON(http.StatusOK, mux.H{
			"files": fileList,
			"count": len(fileList),
		})
	})

	log.Println("Starting file upload demo server on :8080")
	log.Println("Visit http://localhost:8080 to test file uploads")
	log.Println("Upload directory: ./files")
	log.Println("Max multipart memory:", router.MaxMultipartMemory/(1<<20), "MiB")

	if err := router.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
